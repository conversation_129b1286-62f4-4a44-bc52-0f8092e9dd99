{"code": 200, "statusText": "", "message": "", "data": {"feConfigs": {"lafEnv": "https://laf.dev", "mcpServerProxyEndpoint": "http://192.168.23.202:3005", "show_emptyChat": true, "show_git": true, "docUrl": "https://doc.tryfastgpt.ai", "openAPIDocUrl": "https://doc.tryfastgpt.ai/docs/development/openapi", "systemPluginCourseUrl": "https://fael3z0zfze.feishu.cn/wiki/ERZnw9R26iRRG0kXZRec6WL9nwh", "appTemplateCourse": "https://fael3z0zfze.feishu.cn/wiki/CX9wwMGyEi5TL6koiLYcg7U0nWb?fromScene=spaceOverview", "systemTitle": "FastGPT", "concatMd": "项目开源地址: [FastGPT GitHub](https://github.com/labring/FastGPT)\n交流群: ![](https://oss.laf.run/otnvvf-imgs/fastgpt-feishu1.png)", "limit": {"exportDatasetLimitMinutes": 0, "websiteSyncLimitMinuted": 0}, "scripts": [], "favicon": "/favicon.ico", "uploadFileMaxSize": 500, "isPlus": false, "show_aiproxy": true, "show_coupon": false, "showCustomPdfParse": false, "customPdfParsePrice": 0}, "systemVersion": "4.9.12", "activeModelList": [{"model": "glm-4-flash", "name": "glm-4-flash", "maxContext": 128000, "maxResponse": 4000, "quoteMaxToken": 120000, "maxTemperature": 0.99, "showTopP": true, "responseFormatList": ["text", "json_object"], "showStopSign": true, "vision": false, "toolChoice": true, "functionCall": false, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "type": "llm", "provider": "ChatGLM", "isActive": true, "isCustom": false}, {"model": "glm-4v-flash", "name": "glm-4v-flash", "maxContext": 8000, "maxResponse": 1000, "quoteMaxToken": 6000, "maxTemperature": 0.99, "showTopP": true, "showStopSign": true, "vision": true, "toolChoice": false, "functionCall": false, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "type": "llm", "provider": "ChatGLM", "isActive": true, "isCustom": false}, {"model": "bge-m3", "name": "bge-m3", "defaultToken": 8096, "maxToken": 8096, "type": "embedding", "provider": "Other", "charsPointsPrice": 0, "inputPrice": "", "outputPrice": "", "isActive": true, "normalization": false, "isDefault": true, "isCustom": false}, {"model": "deepseek-v3", "name": "deepseek-v3", "charsPointsPrice": 0, "inputPrice": "", "outputPrice": "", "isActive": true, "type": "llm", "showTopP": false, "showStopSign": false, "toolChoice": true, "vision": false, "reasoning": false, "datasetProcess": true, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": true, "maxContext": 65536, "quoteMaxToken": 8096, "maxResponse": "", "maxTemperature": "", "provider": "Other", "isCustom": true}], "defaultModels": {"embedding": {"model": "bge-m3", "name": "bge-m3", "defaultToken": 8096, "maxToken": 8096, "type": "embedding", "provider": "Other", "charsPointsPrice": 0, "inputPrice": "", "outputPrice": "", "isActive": true, "requestUrl": "https://cloud.infini-ai.com/maas/v1", "requestAuth": "sk-davb7onvufkrgzlx", "normalization": false, "isDefault": true, "isCustom": false}, "llm": {"model": "glm-4-flash", "name": "glm-4-flash", "maxContext": 128000, "maxResponse": 4000, "quoteMaxToken": 120000, "maxTemperature": 0.99, "showTopP": true, "responseFormatList": ["text", "json_object"], "showStopSign": true, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "provider": "ChatGLM", "isActive": true, "isCustom": false}, "datasetTextLLM": {"model": "glm-4-flash", "name": "glm-4-flash", "maxContext": 128000, "maxResponse": 4000, "quoteMaxToken": 120000, "maxTemperature": 0.99, "showTopP": true, "responseFormatList": ["text", "json_object"], "showStopSign": true, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "provider": "ChatGLM", "isActive": true, "isCustom": false}, "datasetImageLLM": {"model": "glm-4v-flash", "name": "glm-4v-flash", "maxContext": 8000, "maxResponse": 1000, "quoteMaxToken": 6000, "maxTemperature": 0.99, "showTopP": true, "showStopSign": true, "vision": true, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "provider": "ChatGLM", "isActive": true, "isCustom": false}}}}