package com.sinitek.mind.dataset.core.vector;

import com.sinitek.mind.dataset.dto.DatasetTrainingDTO;
import org.springframework.ai.document.Document;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.vectorstore.VectorStore;

import java.util.List;

/**
 * 向量存储顶层接口
 *
 * <AUTHOR>
 * date 2025-07-22
 */
public interface IDatasetVectorStore {

    /**
     * 组装VectorStore
     * @param embeddingModel
     * @return
     */
    VectorStore buildVectorStore(EmbeddingModel embeddingModel);

    /**
     * 通过datasetId获取VectorStore，默认实现
     */
    VectorStore getVectorStoreByDatabaseId(String datasetId);

    /**
     * 进行相似性搜索
     * @param databaseId
     * @param query
     * @return
     */
    List<Document> similaritySearch(String databaseId,String query);

    /**
     * datasetTraining转换为向量数据
     * @param datasetTraining
     */
    void addDatasetTraining(DatasetTrainingDTO datasetTraining);
}
