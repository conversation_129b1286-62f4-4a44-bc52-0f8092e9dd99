package com.sinitek.mind.dataset.core.vector.impl;

import com.sinitek.mind.dataset.core.vector.IDatasetVectorStore;
import com.sinitek.mind.dataset.core.vector.IDatasetVectorStoreFactory;
import com.sinitek.mind.dataset.dto.DatasetTrainingDTO;
import com.sinitek.sirm.common.spring.SpringFactory;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;

import java.util.List;
import java.util.Map;

/**
 * 向量存储通用抽象类
 *
 * <AUTHOR>
 * date 2025-07-26
 */
public abstract class AbstractDatasetVectorStore implements IDatasetVectorStore {

    /**
     * 通过datasetId获取VectorStore，默认实现
     */
    public VectorStore getVectorStoreByDatabaseId(String datasetId) {
        IDatasetVectorStoreFactory datasetVectorStoreFactory = SpringFactory.getBean(IDatasetVectorStoreFactory.class);
        return datasetVectorStoreFactory.getVectorStoreByDatabaseId(datasetId);
    }

    /**
     * 进行相似性搜索
     * @param databaseId
     * @param query
     * @return
     */
    public List<Document> similaritySearch(String databaseId, String query) {
        VectorStore vectorStore = this.getVectorStoreByDatabaseId(databaseId);
        SearchRequest searchRequest = SearchRequest.builder().query(query).topK(3).build();
        return vectorStore.similaritySearch(searchRequest);
    }

    /**
     * datasetTraining转换为向量数据
     * @param datasetTraining
     */
    public void addDatasetTraining(DatasetTrainingDTO datasetTraining) {
        String datasetId = datasetTraining.getDatasetId();

        String text = datasetTraining.getText();
        Document doc = new Document(text);
        Map<String, Object> metadata = doc.getMetadata();
        metadata.put("datasetId", datasetId);
        metadata.put("teamId", datasetTraining.getTeamId());
        metadata.put("collectionId", datasetTraining.getCollectionId());

        VectorStore vectorStore = this.getVectorStoreByDatabaseId(datasetId);
        vectorStore.add(List.of(doc));
    }
}
