package com.sinitek.mind.core.app.dto;

import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.workflow.model.StoreEdgeItemType;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 发布应用请求DTO
 * 对应TypeScript的PostPublishAppProps
 */
@Data
@Schema(description = "发布应用请求参数")
public class PublishAppDTO {

    @NotNull(message = "nodes不能为空")
    @Schema(description = "工作流节点列表", required = true)
    private List<StoreNodeItemType> nodes;

    @NotNull(message = "edges不能为空")
    @Schema(description = "工作流边列表", required = true)
    private List<StoreEdgeItemType> edges;

    @NotNull(message = "chatConfig不能为空")
    @Schema(description = "聊天配置", required = true)
    private AppChatConfigType chatConfig;

    @Schema(description = "是否发布", example = "true")
    private Boolean isPublish;

    @Schema(description = "版本名称", example = "v1.0.0")
    private String versionName;

    @Schema(description = "是否自动保存（如果是自动保存，只会存储一份整个应用，覆盖旧版本）", example = "false")
    private Boolean autoSave;
}