package com.sinitek.mind.core.app.entity;

import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.app.model.AppScheduledTriggerConfigType;
import com.sinitek.mind.core.app.model.PluginData;
import com.sinitek.mind.core.workflow.model.StoreEdgeItemType;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.FieldType;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@Document(collection = "apps")
public class App {

    @Id
    private String id;

    @Field(targetType = FieldType.OBJECT_ID)
    private String parentId;

    private String teamId;

    private String tmbId;
    private String type;
    private String version;

    @Schema(description = "拥有者id")
    private String ownerId;

    private String name;
    private String avatar;
    private String intro;

    private Date updateTime;

    private List<StoreNodeItemType> modules;
    private List<StoreEdgeItemType> edges;
    private PluginData pluginData;

    private AppChatConfigType chatConfig;
    private AppScheduledTriggerConfigType scheduledTriggerConfig;
    private Date scheduledTriggerNextTime;

    private Boolean inited;
    private List<String> teamTags;
    private Boolean inheritPermission = false;

    private Integer defaultPermission;

    /**
     * 子工具ID列表（用于MCP工具集）
     */
    @Field("childToolIds")
    private List<String> childToolIds;

    /**
     * MCP工具集配置
     */
    @Field("mcpToolsConfig")
    private Map<String, Object> mcpToolsConfig;

    /**
     * 工具配置（用于子工具）
     */
    @Field("toolConfig")
    private Map<String, Object> toolConfig;
} 