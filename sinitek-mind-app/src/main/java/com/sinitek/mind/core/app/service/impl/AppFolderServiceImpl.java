package com.sinitek.mind.core.app.service.impl;

import com.sinitek.mind.core.app.common.ParentFolderUtils;
import com.sinitek.mind.core.app.dto.AuthAppDTO;
import com.sinitek.mind.core.app.dto.CreateAppDTO;
import com.sinitek.mind.core.app.dto.GetPathDTO;
import com.sinitek.mind.core.app.dto.ParentIdDTO;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.entity.AppVersion;
import com.sinitek.mind.core.app.enumerate.AppTypeEnum;
import com.sinitek.mind.core.app.enumerate.PerResourceTypeEnum;
import com.sinitek.mind.core.app.model.ParentTreePathItemType;
import com.sinitek.mind.core.app.repository.AppRepository;
import com.sinitek.mind.core.app.repository.AppVersionRepository;
import com.sinitek.mind.core.app.service.IAppFolderService;
import com.sinitek.mind.core.app.service.IAuthAppService;
import com.sinitek.mind.support.operationlog.enumerate.OperationLogEventEnum;
import com.sinitek.mind.support.operationlog.service.IOperationLogService;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.service.ICollaboratorService;
import com.sinitek.mind.support.permission.service.IPermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class AppFolderServiceImpl implements IAppFolderService {

    private final IPermissionService permissionService;

    private final AppVersionRepository appVersionRepository;

    private final AppRepository appRepository;

    private final ICollaboratorService collaboratorService;

    private final IOperationLogService operationLogService;

    private final IAuthAppService authAppService;

    private static final List<AppTypeEnum> APP_FOLDER_TYPE_LIST = List.of(AppTypeEnum.FOLDER);

    private static final String FOLDER_IMG_URL = "/imgs/files/folder.svg";

    @Override
    public void createFolder(CreateAppDTO body) {
        String parentId = body.getParentId();
        String name = body.getName();
        String intro = body.getIntro();
        if (parentId != null) {
            // TODO 需要确认是需要什么样的权限
//            AuthAppReqDTO dto = new AuthAppReqDTO();
//            dto.setAppId(parentId);
//            AuthResult<AppPermission> authResult = permissionService.authApp(dto);
            AuthAppDTO authAppDTO = authAppService.authApp(parentId, PermissionConstant.MANAGER_PER);

            String teamId = authAppDTO.getTeamId();
            String tmbId = authAppDTO.getTmbId();
            ParentIdDTO parentIdDTO = ParentFolderUtils.parseParentIdInMongo(parentId);
            body.setParentId(parentIdDTO.getParentId());
            body.setAvatar(FOLDER_IMG_URL);
            body.setTeamId(teamId);
            body.setTmbId(tmbId);
            body.setType(AppTypeEnum.FOLDER.getValue());
            String appId = onCreateApp(body);

            List<String> resourceClbsAndGroups = collaboratorService.getResourceClbsAndGroups(teamId, parentId, PerResourceTypeEnum.APP.getValue());

            collaboratorService.syncCollaborators(appId, PerResourceTypeEnum.APP.getValue(), resourceClbsAndGroups, teamId);
        } else {
            // TODO 添加资源权限 ResourcePermission
        }
        Map<String, String> param = new HashMap<>();
        param.put("folderName", body.getName());
        operationLogService.addOperationLog(OperationLogEventEnum.CREATE_APP_FOLDER, param);
    }

    @Override
    public List<ParentTreePathItemType> folderPath(GetPathDTO props) throws Exception {
        // TODO 需要确认是需要什么样的权限
//        AuthAppReqDTO dto = new AuthAppReqDTO();
//        dto.setAppId(props.getSourceId());
//        AuthResult<AppPermission> authResult = permissionService.authApp(dto);
        AuthAppDTO authAppDTO = authAppService.authApp(props.getSourceId(), PermissionConstant.READ_PER);
        App app = authAppDTO.getApp();
        return getParents("current".equals(props.getType()) ? app.getId() : app.getParentId());
    }

    private List<ParentTreePathItemType> getParents(String appId) {
        if (appId == null) {
            return List.of();
        }
        App parent = appRepository.findById(appId).orElse(null);
        if (parent == null) {
            return List.of();
        }
        List<ParentTreePathItemType> parentsPath = getParents(parent.getParentId());

        parentsPath.add(new ParentTreePathItemType(appId, parent.getName()));
        return parentsPath;
    }

    private String onCreateApp(CreateAppDTO body) {
        App app = new App();
        app.setName(body.getName());
        app.setAvatar(body.getAvatar());
        app.setType(body.getType());
        app.setModules(body.getModules());
        app.setEdges(body.getEdges());
        app.setChatConfig(body.getChatConfig());
        app.setVersion("v2");

        // 如果有父级ID，设置父级关系
        if (body.getParentId() != null) {
            app.setParentId(body.getParentId());
        }
        // 保存应用
        App savedApp = appRepository.save(app);
        String appId = savedApp.getId();

        if (!APP_FOLDER_TYPE_LIST.contains(AppTypeEnum.fromValue(savedApp.getType()))) {
            AppVersion appVersion = new AppVersion();
            appVersion.setAppId(appId);
            appVersion.setNodes(body.getModules());
            appVersion.setEdges(body.getEdges());
            appVersion.setChatConfig(body.getChatConfig());
            appVersion.setVersionName(body.getName());

            appVersionRepository.save(appVersion);
        }

        return appId;
    }
}
