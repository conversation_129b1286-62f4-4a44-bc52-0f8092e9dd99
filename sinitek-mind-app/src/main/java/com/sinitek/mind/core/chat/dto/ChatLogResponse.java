package com.sinitek.mind.core.chat.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 聊天日志响应数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatLogResponse {
    
    /**
     * 文档ID
     */
    private String _id;
    
    /**
     * 聊天ID
     */
    private String id;
    
    /**
     * 聊天标题
     */
    private String title;
    
    /**
     * 自定义标题
     */
    private String customTitle;
    
    /**
     * 来源
     */
    private String source;
    
    /**
     * 来源名称
     */
    private String sourceName;
    
    /**
     * 时间
     */
    private LocalDateTime time;
    
    /**
     * 消息数量
     */
    private Integer messageCount;
    
    /**
     * 用户好评反馈数量
     */
    private Integer userGoodFeedbackCount;
    
    /**
     * 用户差评反馈数量
     */
    private Integer userBadFeedbackCount;
    
    /**
     * 自定义反馈数量
     */
    private Integer customFeedbacksCount;
    
    /**
     * 标记数量
     */
    private Integer markCount;
    
    /**
     * 外部链接UID
     */
    private String outLinkUid;
    
    /**
     * 团队成员ID
     */
    private String tmbId;
}