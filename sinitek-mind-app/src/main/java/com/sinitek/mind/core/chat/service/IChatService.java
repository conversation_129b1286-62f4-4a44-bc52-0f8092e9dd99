package com.sinitek.mind.core.chat.service;

import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.core.chat.dto.*;
import com.sinitek.mind.core.chat.entity.Chat;
import com.sinitek.mind.core.chat.entity.ChatItem;
import com.sinitek.mind.core.chat.model.ChatItemType;
import com.sinitek.mind.core.workflow.model.ChatNodeUsageType;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.Map;

public interface IChatService {
    /**
     * 初始化聊天
     *
     * @param request 初始化聊天请求
     * @return 初始化聊天响应
     */
    InitChatResponse initChat(InitChatRequest request);


    /**
     * 分页获取聊天记录
     * @param request 分页请求参数
     * @return 分页聊天记录响应
     */
    PageResult<ChatItem> getPaginationRecords(GetPaginationRecordsRequest request);


    /**
     * 聊天测试
     * @param request 聊天测试请求
     */
    void processChatTest(ChatTestRequest request, SseEmitter emitter);

    /**
     * 分页获取聊天历史
     * @param request 分页请求参数
     * @return 分页聊天历史响应
     */
    PageResult<Chat> getHistories(GetHistoriesRequest request);

    void saveChat(SaveChatDTO dto);

    void updateInteractiveChat(String chatId, String appId, String userInteractiveVal,
                               ChatItemType aiResponse, Map<String, Object> newVariables,
                               Double durationSeconds);

    int createChatUsage(String appName, String appId, String teamId, String tmbId,
                    String source, List<ChatNodeUsageType> flowUsages);

    /**
     * 添加自定义反馈
     * @param appId 应用ID
     * @param chatId 聊天ID
     * @param dataId 数据ID
     * @param feedbacks 反馈列表
     */
    void addCustomFeedbacks(String appId, String chatId, String dataId, List<String> feedbacks);
}
