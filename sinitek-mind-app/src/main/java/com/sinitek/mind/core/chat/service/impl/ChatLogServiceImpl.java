package com.sinitek.mind.core.chat.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.core.app.dto.ExportChatLogsBody;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.util.CustomAggregationOperation;
import com.sinitek.mind.core.chat.dto.ChatLogResponse;
import com.sinitek.mind.core.chat.dto.GetChatLogsRequest;
import com.sinitek.mind.core.chat.repository.ChatItemRepository;
import com.sinitek.mind.core.chat.repository.ChatRepository;
import com.sinitek.mind.core.chat.service.IChatLogService;
import com.sinitek.mind.support.operationlog.enumerate.OperationLogEventEnum;
import com.sinitek.mind.support.operationlog.service.IOperationLogService;
import com.sinitek.mind.support.permission.service.IPermissionService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.PrintWriter;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChatLogServiceImpl implements IChatLogService {

    private final ChatRepository chatRepository;

    private final ChatItemRepository chatItemRepository;

    private final IPermissionService permissionService;

    private final MongoTemplate mongoTemplate;

    private final IOperationLogService operationLogService;

    /**
     * 获取聊天日志列表
     * @param request 请求参数
     * @param teamId 团队ID
     * @param tmbId 团队成员ID
     * @return 分页响应数据
     */
    @Override
    public PageResult<ChatLogResponse> getChatLogs(GetChatLogsRequest request, String teamId, String tmbId, App app) {
        PageResult<ChatLogResponse> result = new PageResult<>();
        // 构建查询条件
        Criteria criteria = buildCriteria(request, teamId);

        // 构建聚合管道
        Aggregation aggregation = buildAggregation(criteria, request);

        // 执行聚合查询
        List<ChatLogResponse> list = mongoTemplate.aggregate(aggregation, "chats", ChatLogResponse.class)
                .getMappedResults();

        // 获取总数
        long total = getTotalCount(request, teamId);
        result.setTotal((int) total);

        // 处理来源成员信息
        List<ChatLogResponse> processedList = addSourceMemberInfo(list);

        // 过滤掉有tmbId的记录
        List<ChatLogResponse> filteredList = processedList.stream()
                .filter(item -> item.getTmbId() == null || item.getTmbId().isEmpty())
                .toList();

        // 记录操作日志（异步）
        Map<String, String> params = new HashMap<>();
        params.put("appName", app.getName());
        params.put("appType", app.getType());
        operationLogService.addOperationLog(OperationLogEventEnum.EXPORT_APP_CHAT_LOG, params);
        // TODO  processedList 联合 filteredList
        result.setList(processedList);
        return result;
    }

    /**
     * 导出聊天日志为JSON格式
     * @param request 导出请求参数
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    @Override
    public void exportChatLogs(ExportChatLogsBody request, String teamId, HttpServletResponse response) throws IOException {
        // 从认证信息中获取teamId，这里暂时使用默认值
        execExport(request, teamId, response);
    }

    /**
     * 构建查询条件
     */
    private Criteria buildCriteria(GetChatLogsRequest request, String teamId) {
        Criteria criteria = Criteria.where("teamId").is(teamId)
                .and("appId").is(request.getAppId())
                .and("updateTime").gte(request.getDateStart()).lte(request.getDateEnd());

        // 添加来源条件
        if (request.getSources() != null && !request.getSources().isEmpty()) {
            criteria.and("source").in(request.getSources());
        }

        // 添加标题搜索条件
        if (request.getLogTitle() != null && !request.getLogTitle().trim().isEmpty()) {
            String escapedTitle = escapeRegexChars(request.getLogTitle());
            Pattern pattern = Pattern.compile(escapedTitle, Pattern.CASE_INSENSITIVE);
            criteria.orOperator(
                    Criteria.where("title").regex(pattern),
                    Criteria.where("customTitle").regex(pattern)
            );
        }

        return criteria;
    }

    /**
     * 构建聚合管道
     */
    private Aggregation buildAggregation(Criteria criteria, GetChatLogsRequest request) {
        // 简化的聚合查询，先获取基本的聊天记录
        return Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.sort(Sort.Direction.DESC, "updateTime"),
                Aggregation.skip(request.getOffset().longValue()),
                Aggregation.limit(request.getPageSize()),
                // Lookup chatitems collection to get message counts and feedback data
                Aggregation.lookup("chatitems", "chatId", "chatId", "chatItems"),
                // Add computed fields using custom aggregation operation
                new CustomAggregationOperation(new Document("$addFields", new Document()
                        .append("messageCount", new Document("$size", new Document("$ifNull", Arrays.asList("$chatItems", Arrays.asList()))))
                        .append("userGoodFeedbackCount", new Document("$size", new Document("$filter", new Document()
                                .append("input", new Document("$ifNull", Arrays.asList("$chatItems", Arrays.asList())))
                                .append("cond", new Document("$eq", Arrays.asList("$$this.userGoodFeedback", true))))))
                        .append("userBadFeedbackCount", new Document("$size", new Document("$filter", new Document()
                                .append("input", new Document("$ifNull", Arrays.asList("$chatItems", Arrays.asList())))
                                .append("cond", new Document("$eq", Arrays.asList("$$this.userBadFeedback", true))))))
                        .append("customFeedbacksCount", new Document("$size", new Document("$filter", new Document()
                                .append("input", new Document("$ifNull", Arrays.asList("$chatItems", Arrays.asList())))
                                .append("cond", new Document("$gt", Arrays.asList(
                                        new Document("$size", new Document("$ifNull", Arrays.asList("$$this.customFeedbacks", Arrays.asList()))), 0))))))
                        .append("markCount", new Document("$size", new Document("$filter", new Document()
                                .append("input", new Document("$ifNull", Arrays.asList("$chatItems", Arrays.asList())))
                                .append("cond", new Document("$eq", Arrays.asList("$$this.adminFeedback", true)))))))),
                // Project final fields
                Aggregation.project()
                        .and("_id").as("_id")
                        .and("chatId").as("id")
                        .and("title").as("title")
                        .and("customTitle").as("customTitle")
                        .and("source").as("source")
                        .and("sourceName").as("sourceName")
                        .and("updateTime").as("time")
                        .and("outLinkUid").as("outLinkUid")
                        .and("tmbId").as("tmbId")
                        .and("messageCount").as("messageCount")
                        .and("userGoodFeedbackCount").as("userGoodFeedbackCount")
                        .and("userBadFeedbackCount").as("userBadFeedbackCount")
                        .and("customFeedbacksCount").as("customFeedbacksCount")
                        .and("markCount").as("markCount")
        );
    }

    /**
     * 获取总数
     */
    private long getTotalCount(GetChatLogsRequest request, String teamId) {
        try {
            boolean hasSources = request.getSources() != null && !request.getSources().isEmpty();
            boolean hasTitle = request.getLogTitle() != null && !request.getLogTitle().trim().isEmpty();

            log.debug("Getting total count for teamId: {}, appId: {}, hasSources: {}, hasTitle: {}",
                    teamId, request.getAppId(), hasSources, hasTitle);

            Long count = null;
            if (hasSources && hasTitle) {
                String titleRegex = escapeRegexChars(request.getLogTitle());
                count = chatRepository.countByConditionsWithSourcesAndTitle(
                        teamId, request.getAppId(), request.getDateStart(), request.getDateEnd(),
                        request.getSources(), titleRegex
                );
            } else if (hasSources) {
                count = chatRepository.countByConditionsWithSources(
                        teamId, request.getAppId(), request.getDateStart(), request.getDateEnd(),
                        request.getSources()
                );
            } else if (hasTitle) {
                String titleRegex = escapeRegexChars(request.getLogTitle());
                count = chatRepository.countByConditionsWithTitle(
                        teamId, request.getAppId(), request.getDateStart(), request.getDateEnd(),
                        titleRegex
                );
            } else {
                count = chatRepository.countByConditions(
                        teamId, request.getAppId(), request.getDateStart(), request.getDateEnd()
                );
            }

            return count;

        } catch (Exception e) {
            log.error("Error getting total count for teamId: {}, appId: {}", teamId, request.getAppId(), e);
            return 0;
        }
    }

    /**
     * 转义正则表达式特殊字符
     */
    private String escapeRegexChars(String input) {
        if (input == null) return "";
        return input.replaceAll("[\\^$.*+?()\\[\\]{}|]", "\\\\$0");
    }

    /**
     * 添加来源成员信息
     */
    private List<ChatLogResponse> addSourceMemberInfo(List<ChatLogResponse> list) {
        // 这里可以添加来源成员信息的逻辑
        // 暂时直接返回原列表
        return list;
    }

    /**
     * 导出聊天日志为CSV文件
     * @param request 导出请求参数
     * @param teamId 团队ID
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    public void execExport(ExportChatLogsBody request, String teamId, HttpServletResponse response) throws IOException {
        // 设置响应头
        response.setContentType("text/csv; charset=utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=chatlog_export.csv");

        PrintWriter writer = response.getWriter();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        try {
            // 写入BOM头，确保Excel正确识别UTF-8编码
            writer.write("\uFEFF");

            // 写入标题
            writer.println(request.getTitle());

            // 写入CSV表头
            writer.println("时间,来源,用户名,联系方式,标题,消息数量,好评反馈项,差评反馈项,自定义反馈项,标记项,聊天详情");

            // 构建查询条件
            Criteria criteria = buildExportCriteria(request, teamId);

            // 构建导出聚合查询
            Aggregation aggregation = buildExportAggregation(criteria);

            // 执行聚合查询
            List<Document> results = mongoTemplate.aggregate(aggregation, "chats", Document.class)
                    .getMappedResults();

            // 处理每条记录并写入CSV
            for (Document doc : results) {
                try {
                    // 格式化时间
                    String timeStr = "";
                    if (doc.getDate("time") != null) {
                        timeStr = doc.getDate("time").toInstant()
                                .atZone(ZoneId.systemDefault())
                                .format(formatter);
                    }

                    // 获取来源信息
                    String source = doc.getString("source");
                    String sourceName = source;
                    if (source != null && request.getSourcesMap() != null) {
                        ExportChatLogsBody.SourceInfo sourceInfo = request.getSourcesMap().get(source);
                        if (sourceInfo != null && sourceInfo.getLabel() != null) {
                            sourceName = sourceInfo.getLabel();
                        }
                    }

                    // 获取其他字段
                    String userId = doc.getString("outLinkUid");
                    String title = doc.getString("customTitle");
                    if (title == null || title.trim().isEmpty()) {
                        title = doc.getString("title");
                    }
                    Integer messageCount = doc.getInteger("messageCount", 0);

                    // 处理反馈数据
                    List<?> userGoodFeedbackItems = (List<?>) doc.get("userGoodFeedbackItems");
                    List<?> userBadFeedbackItems = (List<?>) doc.get("userBadFeedbackItems");
                    List<?> customFeedbackItems = (List<?>) doc.get("customFeedbackItems");
                    List<?> markItems = (List<?>) doc.get("markItems");
                    List<?> chatDetails = (List<?>) doc.get("chatDetails");

                    // 格式化JSON字符串用于CSV
                    String userGoodFeedbackItemsStr = formatJsonString(userGoodFeedbackItems != null ? userGoodFeedbackItems : new ArrayList<>());
                    String userBadFeedbackItemsStr = formatJsonString(userBadFeedbackItems != null ? userBadFeedbackItems : new ArrayList<>());
                    String customFeedbackItemsStr = formatJsonString(customFeedbackItems != null ? customFeedbackItems : new ArrayList<>());
                    String markItemsStr = formatJsonString(markItems != null ? markItems : new ArrayList<>());
                    String chatDetailsStr = formatJsonString(chatDetails != null ? chatDetails : new ArrayList<>());

                    // 写入CSV行
                    writer.printf("%s,%s,%s,%s,%s,%d,\"%s\",\"%s\",\"%s\",\"%s\",\"%s\"%n",
                            formatCsvField(timeStr),
                            formatCsvField(sourceName != null ? sourceName : ""),
                            formatCsvField(userId != null ? userId : ""),
                            "", // 联系方式字段暂时为空
                            formatCsvField(title != null ? title : ""),
                            messageCount,
                            userGoodFeedbackItemsStr.replace("\"", "\"\""),
                            userBadFeedbackItemsStr.replace("\"", "\"\""),
                            customFeedbackItemsStr.replace("\"", "\"\""),
                            markItemsStr.replace("\"", "\"\""),
                            chatDetailsStr.replace("\"", "\"\"")
                    );

                } catch (Exception e) {
                    log.error("处理导出记录时出错", e);
                    continue;
                }
            }

            writer.flush();

        } catch (Exception e) {
            log.error("导出聊天日志失败", e);
            throw new RuntimeException("导出聊天日志失败: " + e.getMessage(), e);
        } finally {
            if (writer != null) {
                writer.close();
            }
        }
    }

    /**
     * 构建导出查询条件
     */
    private Criteria buildExportCriteria(ExportChatLogsBody request, String teamId) {
        // 转换日期字符串为LocalDateTime
        Date dateStart = request.getDateStart();
        Date dateEnd = request.getDateEnd();

        Criteria criteria = Criteria.where("teamId").is(teamId)
                .and("appId").is(request.getAppId())
                .and("updateTime").gte(dateStart).lte(dateEnd);

        // 添加来源条件
        if (request.getSources() != null && !request.getSources().isEmpty()) {
            criteria.and("source").in(request.getSources());
        }

        // 添加标题搜索条件
        if (request.getLogTitle() != null && !request.getLogTitle().trim().isEmpty()) {
            String escapedTitle = escapeRegexChars(request.getLogTitle());
            Pattern pattern = Pattern.compile(escapedTitle, Pattern.CASE_INSENSITIVE);
            criteria.orOperator(
                    Criteria.where("title").regex(pattern),
                    Criteria.where("customTitle").regex(pattern)
            );
        }

        return criteria;
    }

    /**
     * 构建导出聚合管道
     */
    private Aggregation buildExportAggregation(Criteria criteria) {
        return Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.sort(Sort.by(Sort.Direction.DESC, "userBadFeedbackCount", "userGoodFeedbackCount", "customFeedbacksCount", "updateTime")),
                Aggregation.limit(50000),
                // Lookup chatitems collection
                Aggregation.lookup("chatitems", "chatId", "chatId", "chatitems"),
                // Add computed fields
                new CustomAggregationOperation(new Document("$addFields", new Document()
                        .append("userGoodFeedbackItems", new Document("$filter", new Document()
                                .append("input", "$chatitems")
                                .append("as", "item")
                                .append("cond", new Document("$ifNull", Arrays.asList("$$item.userGoodFeedback", false)))))
                        .append("userBadFeedbackItems", new Document("$filter", new Document()
                                .append("input", "$chatitems")
                                .append("as", "item")
                                .append("cond", new Document("$ifNull", Arrays.asList("$$item.userBadFeedback", false)))))
                        .append("customFeedbackItems", new Document("$filter", new Document()
                                .append("input", "$chatitems")
                                .append("as", "item")
                                .append("cond", new Document("$gt", Arrays.asList(
                                        new Document("$size", new Document("$ifNull", Arrays.asList("$$item.customFeedbacks", List.of()))), 0)))))
                        .append("markItems", new Document("$filter", new Document()
                                .append("input", "$chatitems")
                                .append("as", "item")
                                .append("cond", new Document("$ifNull", Arrays.asList("$$item.adminFeedback", false)))))
                        .append("chatDetails", new Document("$map", new Document()
                                .append("input", new Document("$slice", Arrays.asList("$chatitems", -1000)))
                                .append("as", "item")
                                .append("in", new Document()
                                        .append("id", "$$item._id")
                                        .append("value", "$$item.value")))))),
                // Project final fields
                new CustomAggregationOperation(new Document("$project", new Document()
                        .append("_id", "$_id")
                        .append("id", "$chatId")
                        .append("title", "$title")
                        .append("customTitle", "$customTitle")
                        .append("source", "$source")
                        .append("time", "$updateTime")
                        .append("outLinkUid", "$outLinkUid")
                        .append("tmbId", "$tmbId")
                        .append("messageCount", new Document("$size", new Document("$ifNull", Arrays.asList("$chatitems", Arrays.asList()))))
                        .append("userGoodFeedbackItems", "$userGoodFeedbackItems")
                        .append("userBadFeedbackItems", "$userBadFeedbackItems")
                        .append("customFeedbackItems", "$customFeedbackItems")
                        .append("markItems", "$markItems")
                        .append("chatDetails", "$chatDetails")))
        );
    }

    /**
     * 格式化JSON字符串用于CSV
     */
    private String formatJsonString(Object data) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonString = objectMapper.writeValueAsString(data);
            return jsonString.replace("\n", "\\n");
        } catch (Exception e) {
            log.error("格式化JSON字符串失败", e);
            return "";
        }
    }

    /**
     * 格式化CSV字段
     */
    private String formatCsvField(String field) {
        if (field == null) {
            return "";
        }
        // 转义双引号并用双引号包围包含逗号、换行符或双引号的字段
        if (field.contains(",") || field.contains("\n") || field.contains("\"")) {
            return "\"" + field.replace("\"", "\"\"") + "\"";
        }
        return field;
    }
}
