package com.sinitek.mind.dataset.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.dataset.constant.DatasetConstant;
import com.sinitek.mind.dataset.convert.DatasetConvert;
import com.sinitek.mind.dataset.core.reader.VectorFileReaderCore;
import com.sinitek.mind.dataset.dto.*;
import com.sinitek.mind.dataset.entity.Dataset;
import com.sinitek.mind.dataset.enumerate.DatasetStatusEnum;
import com.sinitek.mind.dataset.enumerate.PreviewChunksTypeEnum;
import com.sinitek.mind.dataset.service.IDatasetService;
import com.sinitek.mind.support.common.dto.FileInfo;
import com.sinitek.mind.support.common.service.IFileService;
import com.sinitek.mind.system.dto.AuthDTO;
import com.sinitek.mind.system.service.AuthService;
import com.sinitek.sirm.framework.exception.BussinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.ai.document.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 知识库控制器
 *
 * <AUTHOR>
 * date 2025-07-02
 * 描述：知识库相关API接口
 */
@RestController
@RequestMapping("/mind/api/core/dataset")
@Tag(name = "知识库管理")
public class DatasetController {

    @Autowired
    private AuthService authService;

    @Autowired
    private IDatasetService datasetService;

    @Autowired
    private DatasetConvert datasetConvert;

    @Autowired
    private VectorFileReaderCore vectorFileReaderCore;

    @Autowired
    private IFileService fileService;

    @PostMapping("/create")
    @Operation(summary = "创建知识库")
    public ApiResponse<String> createDataset(
            @RequestBody DatasetCreateRequest createRequest) throws Exception {
        AuthDTO authInfo = authService.authCert();
        String userId = authInfo.getUserId();
        String teamId = authInfo.getTeamId();
        String tmbId = authInfo.getTmbId();

        String datasetId = datasetService.createDataset(createRequest, userId, teamId, tmbId);
        return ApiResponse.success(datasetId);
    }

    @PostMapping("/update")
    @Operation(summary = "更新知识库")
    public ApiResponse<Void> updateDataset(
            @RequestBody DatasetUpdateRequest updateRequest) throws Exception {
        AuthDTO authInfo = authService.authCert();
        String userId = authInfo.getUserId();
        String teamId = authInfo.getTeamId();
        String tmbId = authInfo.getTmbId();

        datasetService.updateDataset(updateRequest, userId, teamId, tmbId);
        return ApiResponse.success();
    }

    @PostMapping("/delete")
    @Operation(summary = "删除知识库")
    public ApiResponse<Void> deleteDataset(
            @Parameter(description = "知识库ID") @RequestParam String id) throws Exception {
        AuthDTO authInfo = authService.authCert();
        String userId = authInfo.getUserId();
        String teamId = authInfo.getTeamId();
        String tmbId = authInfo.getTmbId();

        datasetService.deleteDataset(id, userId, teamId, tmbId);
        return ApiResponse.success();
    }

    @GetMapping("/detail")
    @Operation(summary = "获取知识库详情")
    public ApiResponse<DatasetDTO> getDatasetDetail(
            @Parameter(description = "知识库ID") @RequestParam String id) throws Exception {
        AuthDTO authInfo = authService.authCert();
        String userId = authInfo.getUserId();
        String teamId = authInfo.getTeamId();
        String tmbId = authInfo.getTmbId();

        DatasetDTO dataset = datasetService.getDatasetDetail(id, userId, teamId, tmbId);
        return ApiResponse.success(dataset);
    }

    @PostMapping("/list")
    @Operation(summary = "获取知识库列表")
    public ApiResponse<List<DatasetDTO>> getDatasetList(
            @RequestBody DatasetListRequest listRequest) throws Exception {
        AuthDTO authInfo = authService.authCert();
        String userId = authInfo.getUserId();
        String teamId = authInfo.getTeamId();
        String tmbId = authInfo.getTmbId();

        List<Dataset> datasets = datasetService.getDatasetList(listRequest, userId, teamId, tmbId);
        List<DatasetDTO> datasetDTOList = this.format(datasets, authInfo);
        return ApiResponse.success(datasetDTOList);
    }

    @GetMapping("/paths")
    @Operation(summary = "获取知识库路径")
    public ApiResponse<List<DatasetPathDTO>> getDatasetPaths(
            @Parameter(description = "知识库ID") @RequestParam(required = false) String id) throws Exception {
        AuthDTO authInfo = authService.authCert();
        String userId = authInfo.getUserId();
        String teamId = authInfo.getTeamId();
        String tmbId = authInfo.getTmbId();

        List<DatasetPathDTO> paths = datasetService.getDatasetPaths(id, userId, teamId, tmbId);

        return ApiResponse.success(paths);
    }

    /**
     * 格式化知识库的列表
     * @param datasets
     * @return
     */
    public List<DatasetDTO> format(List<Dataset> datasets, AuthDTO authInfo) {
        String tmbId = authInfo.getTmbId();
        List<DatasetDTO> resultList = new ArrayList<>();
        for (Dataset dataset : datasets) {
            DatasetDTO dto = datasetConvert.convertToDTO(dataset);
            dto.setPermission(new DatasetPermissionDTO(15, tmbId.equals(dataset.getTmbId())));
            dto.setStatus(DatasetStatusEnum.ACTIVE);

            // TODO: 固定所属成员的信息，等协作能力整体补充完，在根据实际情况处理
            DatasetSourceMemberDTO sourceMember = new DatasetSourceMemberDTO();
            sourceMember.setName("Owner");
            sourceMember.setName("/api/system/img/686ca73df892b2654b7b16ab.jpeg");
            sourceMember.setStatus(DatasetStatusEnum.ACTIVE.getValue());
            dto.setSourceMember(sourceMember);

            resultList.add(dto);
        }
        return resultList;
    }

    @PostMapping("/preview-chunks")
    @Operation(summary = "获取文件预览分块")
    public ApiResponse<PreviewChunksResponseDTO> getPreviewChunks(@RequestBody @Valid PreviewChunksRequestDTO request) {
        String type = request.getType();
        List<Document> documents;
        if (PreviewChunksTypeEnum.FILE_LOCAL.getValue().equals(type)) {
            String fileId = request.getSourceId();
            FileInfo fileInfo = fileService.getFileById(DatasetConstant.DATASET_BUCKET_NAME, fileId);
            File sourceFile = fileInfo.getSourceFile();
            documents = vectorFileReaderCore.process(sourceFile);
        } else {
            throw new BussinessException("不支持的分块预览类型: " + type);
        }

        // 转换Document为PreviewChunksResponse格式
        PreviewChunksResponseDTO response = new PreviewChunksResponseDTO();
        List<PreviewChunksResponseDTO.ChunkDTO> chunks = new ArrayList<>();
        for (Document doc : documents) {
            PreviewChunksResponseDTO.ChunkDTO chunk = new PreviewChunksResponseDTO.ChunkDTO();
            chunk.setQ(doc.getText());
            chunk.setA("");
            chunks.add(chunk);
        }
        response.setChunks(chunks);
        response.setTotal(chunks.size());
    
        return ApiResponse.success(response);
    }
}