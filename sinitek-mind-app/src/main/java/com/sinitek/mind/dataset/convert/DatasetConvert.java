package com.sinitek.mind.dataset.convert;


import com.sinitek.mind.dataset.dto.DatasetDTO;
import com.sinitek.mind.dataset.entity.Dataset;
import com.sinitek.mind.model.service.ISystemModelService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 知识库-转换层
 *
 * <AUTHOR>
 * date 2025-07-15
 */
@Component
public class DatasetConvert {

    @Autowired
    private ISystemModelService systemModelService;

    /**
     * 转换为DTO
     *
     * @param dataset 知识库实体
     * @return 知识库DTO
     */
    public DatasetDTO convertToDTO(Dataset dataset) {
        DatasetDTO dto = new DatasetDTO();
        BeanUtils.copyProperties(dataset, dto);
        dto.set_id(dataset.getId());

        dto.setParentId(dataset.getParentId());
        dto.setTeamId(dataset.getTeamId());
        dto.setTmbId(dataset.getTmbId());

        // 设置模型信息
        if (StringUtils.isNotBlank(dataset.getVectorModel())) {
            dto.setVectorModel(systemModelService.findModelByModelId(dataset.getVectorModel()));
        }

        if (StringUtils.isNotBlank(dataset.getAgentModel())) {
            dto.setAgentModel(systemModelService.findModelByModelId(dataset.getAgentModel()));
        }

        if (StringUtils.isNotBlank(dataset.getVlmModel())) {
            dto.setVlmModel(systemModelService.findModelByModelId(dataset.getVlmModel()));
        }

        return dto;
    }

}
