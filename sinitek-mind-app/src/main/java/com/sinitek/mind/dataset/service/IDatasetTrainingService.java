package com.sinitek.mind.dataset.service;

import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.dataset.dto.DatasetTrainingDTO;
import com.sinitek.mind.dataset.dto.TrainingErrorQueryDTO;
import com.sinitek.mind.dataset.entity.DatasetTraining;

import java.util.List;

/**
 * dataset_training Service层
 *
 * <AUTHOR>
 * date 2025-07-25
 */
public interface IDatasetTrainingService {

    String create(DatasetTrainingDTO dto);

    List<DatasetTraining> findPendingTrainings();

    /**
     * 更新状态为失败
     * @param id
     * @param errorMsg
     */
    void updateStatusForFailed(String id, String errorMsg);

    /**
     * 分页获取集合下训练失败的列表
     * @param queryDTO
     * @return
     */
    PageResult<DatasetTrainingDTO> searchFailedTrainings(TrainingErrorQueryDTO queryDTO);

    /**
     * 删除单个训练失败数据
     * @param datasetId
     * @param collectionId
     * @param dataId
     */
    void removeFailedTraining(String datasetId, String collectionId, String dataId);

    /**
     * 获取单个训练数据详情
     * @param datasetId
     * @param collectionId
     * @param dataId
     * @return
     */
    DatasetTrainingDTO getTrainingDataDetail(String datasetId, String collectionId, String dataId);

    /**
     * 更新训练数据状态为待处理
     * @param datasetId 数据集ID
     * @param collectionId 集合ID
     * @param dataId 数据ID
     * @param q 问题内容
     */
    void updateTrainingStatus(String datasetId, String collectionId, String dataId, String q);

    /**
     * 统计重建中的文档数量
     * @param datasetId 数据集ID
     * @return 重建中数量
     */
    int countRebuildingDocuments(String datasetId);

    /**
     * 统计训练中的文档数量
     * @param datasetId 数据集ID
     * @return 训练中数量
     */
    int countTrainingDocuments(String datasetId);
}