package com.sinitek.mind.dataset.service.impl;

import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.dataset.dto.DatasetTrainingDTO;
import com.sinitek.mind.dataset.dto.TrainingErrorQueryDTO;
import com.sinitek.mind.dataset.entity.DatasetData;
import com.sinitek.mind.dataset.entity.DatasetTraining;
import com.sinitek.mind.dataset.enumerate.DatasetTrainingStatusEnum;
import com.sinitek.mind.dataset.repository.DatasetTrainingRepository;
import com.sinitek.mind.dataset.service.IDatasetTrainingService;
import com.sinitek.sirm.framework.exception.BussinessException;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * dataset_training Service层Impl
 *
 * <AUTHOR>
 * date 2025-07-25
 */
@Service
public class DatasetTrainingServiceImpl implements IDatasetTrainingService {

    @Autowired
    private DatasetTrainingRepository trainingRepository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public String create(DatasetTrainingDTO dto) {
        DatasetTraining entity = new DatasetTraining();
        BeanUtils.copyProperties(dto, entity);
        entity.setStatus(DatasetTrainingStatusEnum.PENDING.getValue());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        DatasetTraining saved = trainingRepository.save(entity);
        return saved.getId();
    }

    @Override
    public List<DatasetTraining> findPendingTrainings() {
        Query query = new Query();
        query.addCriteria(Criteria.where("status").is(DatasetTrainingStatusEnum.PENDING.getValue()));
        query.with(Sort.by(Sort.Order.asc("collectionId"), Sort.Order.asc("createTime")));
        query.limit(100);
        return mongoTemplate.find(query, DatasetTraining.class);
    }

    @Override
    public void updateStatusForFailed(String id, String errorMsg) {
        if (StringUtils.isBlank(id)) {
            throw new BussinessException("ID不能为空");
        }
        Optional<DatasetTraining> optional = trainingRepository.findById(id);
        if (!optional.isPresent()) {
            throw new BussinessException("数据不存在");
        }
        DatasetTraining entity = optional.get();
        Integer retryCount = entity.getRetryCount();
        if (ObjectUtils.isNotEmpty(retryCount)) {
            retryCount = retryCount + 1;
        } else {
            retryCount = 1;
        }
        entity.setRetryCount(retryCount);
        entity.setStatus(DatasetTrainingStatusEnum.FAILED.getValue());
        entity.setUpdateTime(new Date());
        entity.setErrorMsg(errorMsg);
        trainingRepository.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeFailedTraining(String datasetId, String collectionId, String dataId) {
        if (StringUtils.isAnyBlank(datasetId, collectionId, dataId)) {
            throw new BussinessException("数据集ID、集合ID和数据ID不能为空");
        }

        Criteria criteria = new Criteria();
        criteria.and("status").is(DatasetTrainingStatusEnum.FAILED.getValue());
        criteria.and("datasetId").is(datasetId);
        criteria.and("collectionId").is(collectionId);
        criteria.and("dataId").is(dataId);

        Query query = new Query(criteria);
        mongoTemplate.remove(query, DatasetTraining.class);
    }

    @Override
    public DatasetTrainingDTO getTrainingDataDetail(String datasetId, String collectionId, String dataId) {
        if (StringUtils.isAnyBlank(datasetId, collectionId, dataId)) {
            throw new BussinessException("数据集ID、集合ID和数据ID不能为空");
        }

        Criteria criteria = new Criteria();
        criteria.and("datasetId").is(datasetId)
                .and("collectionId").is(collectionId)
                .and("dataId").is(dataId);

        Query query = new Query(criteria);
        DatasetTraining entity = mongoTemplate.findOne(query, DatasetTraining.class);
        if (entity == null) {
            throw new BussinessException("训练数据不存在");
        }
        return convertToDTO(entity);
    }

    @Override
    public PageResult<DatasetTrainingDTO> searchFailedTrainings(TrainingErrorQueryDTO queryDTO) {
        if (queryDTO == null || StringUtils.isBlank(queryDTO.getCollectionId())) {
            throw new BussinessException("集合ID不能为空");
        }

        // 创建查询条件
        Criteria criteria = new Criteria();
        criteria.and("status").is(DatasetTrainingStatusEnum.FAILED.getValue());
        criteria.and("collectionId").is(queryDTO.getCollectionId());

        // 构建查询
        Query query = new Query(criteria);

        // 计算总记录数
        long total = mongoTemplate.count(query, DatasetTraining.class);

        // 设置分页参数
        int pageNumber = queryDTO.getOffset() / queryDTO.getPageSize();
        query.skip((long) pageNumber * queryDTO.getPageSize());
        query.limit(queryDTO.getPageSize());

        // 按创建时间降序排序
        query.with(Sort.by(Sort.Direction.ASC, "chunkIndex"));

        // 执行查询
        List<DatasetTraining> trainingList = mongoTemplate.find(query, DatasetTraining.class);

        // 转换为DTO列表
        List<DatasetTrainingDTO> dtoList = trainingList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        // 构建分页结果
        PageResult<DatasetTrainingDTO> pageResult = new PageResult<>();
        pageResult.setList(dtoList);
        pageResult.setTotal((int) total);
        return pageResult;
    }

    @Override
    public void updateTrainingStatus(String datasetId, String collectionId, String dataId, String q) {
        if (StringUtils.isAnyBlank(datasetId, collectionId, dataId)) {
            throw new BussinessException("数据集ID、集合ID和数据ID不能为空");
        }

        Criteria criteria = new Criteria();
        criteria.and("datasetId").is(datasetId)
                .and("collectionId").is(collectionId)
                .and("dataId").is(dataId)
                .and("status").is(DatasetTrainingStatusEnum.FAILED.getValue());

        Query query = new Query(criteria);
        DatasetTraining entity = mongoTemplate.findOne(query, DatasetTraining.class);
        if (entity == null) {
            throw new BussinessException("未找到失败的训练数据");
        }
        entity.setStatus(DatasetTrainingStatusEnum.PENDING.getValue());
        entity.setUpdateTime(new Date());
        entity.setErrorMsg(null);
        
        if (StringUtils.isNotBlank(q)) {
            entity.setText(q);
            
            Query dataQuery = new Query(Criteria.where("_id").is(dataId));
            Update dataUpdate = new Update().set("q", q);
            mongoTemplate.updateFirst(dataQuery, dataUpdate, DatasetData.class);
        }
        
        trainingRepository.save(entity);
    }

    private DatasetTrainingDTO convertToDTO(DatasetTraining entity) {
        DatasetTrainingDTO dto = new DatasetTrainingDTO();
        BeanUtils.copyProperties(entity, dto);
        dto.set_id(entity.getId()); // 将MongoDB的_id映射到DTO的_id字段
        return dto;
    }

    @Override
    public int countRebuildingDocuments(String datasetId) {
        if (StringUtils.isBlank(datasetId)) {
            throw new BussinessException("数据集ID不能为空");
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("rebuilding").is(true).and("datasetId").is(datasetId));
        return (int) mongoTemplate.count(query, "datasetData");
    }

    @Override
    public int countTrainingDocuments(String datasetId) {
        if (StringUtils.isBlank(datasetId)) {
            throw new BussinessException("数据集ID不能为空");
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("datasetId").is(datasetId));
        return (int) mongoTemplate.count(query, DatasetTraining.class);
    }
}