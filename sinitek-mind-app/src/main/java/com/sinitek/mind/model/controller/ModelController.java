package com.sinitek.mind.model.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.util.DefaultIndenter;
import com.fasterxml.jackson.core.util.DefaultPrettyPrinter;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.model.dto.*;
import com.sinitek.mind.model.entity.SystemModel;
import com.sinitek.mind.model.repository.SystemModelRepository;
import com.sinitek.mind.model.service.IModelTestService;
import com.sinitek.mind.model.service.ISystemModelService;
import com.sinitek.mind.model.support.ModelConfigManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 模型控制器
 *
 * <AUTHOR>
 * date 2025-07-07
 */
@Tag(name = "模型接口", description = "模型相关操作接口")
@RestController
@RequestMapping("/mind/api/core/ai/model")
public class ModelController {

    @Autowired
    private ISystemModelService systemModelService;

    @Autowired
    private IModelTestService modelTestService;

    @Autowired
    private ModelConfigManager modelConfigManager;

    @Autowired
    private SystemModelRepository systemModelRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @GetMapping("/getConfigJson")
    @Operation(summary = "获取配置文件")
    public ApiResponse<String> getConfigJson() {
        List<SystemModel> dbModels = systemModelRepository.findAll();
        List<Map<String, Object>> result = dbModels.stream()
            .map(model -> {
                Map<String, Object> item = new HashMap<>();
                item.put("model", model.getModel());
                item.put("metadata", model.getMetadata());
                return item;
            })
            .collect(Collectors.toList());
        try {
            DefaultPrettyPrinter prettyPrinter = new DefaultPrettyPrinter();
            DefaultPrettyPrinter.Indenter indenter = new DefaultIndenter("  ", "\n");
            prettyPrinter.indentArraysWith(indenter);
            prettyPrinter.indentObjectsWith(indenter);
            String json = objectMapper.writer(prettyPrinter).writeValueAsString(result);
            return ApiResponse.success(json);
        } catch (JsonProcessingException e) {
            return ApiResponse.error("JSON格式化失败: " + e.getMessage());
        }
    }

    @PostMapping("/updateWithJson")
    @Operation(summary = "更新配置文件")
    public ApiResponse<List<SystemModelDTO>> updateWithJson(@RequestBody ModelUpdateWithJsonDTO update) throws Exception {
        String configStr = update.getConfig();
        systemModelService.updateWithJson(configStr);
        return ApiResponse.success();
    }

    @GetMapping("/list")
    @Operation(summary = "获取所有模型列表")
    public ApiResponse<List<ModelListResponse>> list() throws Exception {
        return ApiResponse.success(systemModelService.listModels());
    }

    @GetMapping("/detail")
    @Operation(summary = "获取模型详情")
    public ApiResponse<SystemModelDTO> detail(
            @Parameter(description = "模型标识") @RequestParam String model) throws Exception {
        SystemModelDTO modelItem = systemModelService.getModelDetail(model);
        return ApiResponse.success(modelItem);
    }
    
    @GetMapping("/test")
    @Operation(summary = "测试模型")
    public ApiResponse<Object> test(
            @Parameter(description = "模型标识") @RequestParam String model) throws Exception {
        try {
            // 查找模型信息
            SystemModelDTO modelInfo = systemModelService.findModelByModelId(model);

            // 统一使用ModelTestService进行测试
            Map<String, String> headers = new HashMap<>();
            Object testResult = modelTestService.testModel(modelInfo, headers);
            
            return ApiResponse.success(testResult);
        } catch (Exception e) {
            return ApiResponse.error("模型测试失败: " + e.getMessage());
        }
    }
    
    @PostMapping("/update")
    @Operation(summary = "更新模型")
    public ApiResponse<Void> update(
            @RequestBody ModelUpdateRequest updateRequest) throws Exception {
        systemModelService.updateModel(updateRequest);
        return ApiResponse.success();
    }
    
    @PostMapping("/updateDefault")
    @Operation(summary = "更新默认模型")
    public ApiResponse<Void> updateDefault(
            @RequestBody ModelUpdateDefaultRequest updateRequest) throws Exception {
        systemModelService.updateDefaultModel(updateRequest);
        return ApiResponse.success();
    }
    
    @PostMapping("/delete")
    @Operation(summary = "删除模型")
    public ApiResponse<Void> delete(
            @Parameter(description = "模型标识") @RequestBody ModelDeleteDTO modelDeleteDTO) throws Exception {
        String model = modelDeleteDTO.getModel();
        systemModelService.deleteModel(model);
        return ApiResponse.success();
    }
}