package com.sinitek.mind.model.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.model.dto.ModelListResponse;
import com.sinitek.mind.model.dto.ModelUpdateDefaultRequest;
import com.sinitek.mind.model.dto.ModelUpdateRequest;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.entity.SystemModel;
import com.sinitek.mind.model.repository.SystemModelRepository;
import com.sinitek.mind.model.service.IModelTestService;
import com.sinitek.mind.model.service.ISystemModelService;
import com.sinitek.mind.model.support.ModelConfigManager;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统模型服务实现类
 *
 * <AUTHOR>
 * date 2025-07-01
 * 描述：处理模型管理相关的业务逻辑实现
 */
@Slf4j
@Service
public class SystemModelServiceImpl implements ISystemModelService {

    @Autowired
    private SystemModelRepository systemModelRepository;
    
    @Autowired
    private ModelConfigManager modelConfigManager;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private IModelTestService modelTestService;
    
    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 获取所有模型列表
     */
    @Override
    public List<ModelListResponse> listModels() {
        List<SystemModelDTO> modelList = ModelConfigManager.getSystemModelList();

        // DB上激活的模型列表
        List<SystemModelDTO> systemActiveModelList = ModelConfigManager.getSystemActiveModelList();
        List<String> activeModelIdList = systemActiveModelList.stream().map(SystemModelDTO::getModel).collect(Collectors.toUnmodifiableList());

        List<SystemModelDTO> allModelList = new ArrayList<>(systemActiveModelList);
        for (SystemModelDTO systemModelDTO : modelList) {
            String model = systemModelDTO.getModel();
            // 激活的跳过
            if (activeModelIdList.contains(model)) {
                continue;
            }
            allModelList.add(systemModelDTO);
        }

        return allModelList.stream()
            .map(model -> {
                ModelListResponse dto = new ModelListResponse();
                // 按照Node.js项目的字段顺序设置
                dto.setType(model.getType());
                dto.setName(model.getName());
                dto.setAvatar(model.getAvatar());
                dto.setProvider(model.getProvider());
                dto.setModel(model.getModel());
                dto.setCharsPointsPrice(model.getCharsPointsPrice());
                dto.setInputPrice(model.getInputPrice());
                dto.setOutputPrice(model.getOutputPrice());
                dto.setIsActive(model.getIsActive() != null ? model.getIsActive() : false);
                dto.setIsCustom(model.getIsCustom() != null ? model.getIsCustom() : false);
                // contextToken 兼容 maxContext/maxToken
                if (model.getMaxContext() != null) {
                    dto.setContextToken(model.getMaxContext());
                } else if (model.getMaxToken() != null) {
                    dto.setContextToken(model.getMaxToken());
                }
                dto.setVision(model.getVision());
                dto.setToolChoice(model.getToolChoice());
                return dto;
            })
            .collect(Collectors.toList());
    }

    /**
     * 获取模型详情
     */
    @Override
    public SystemModelDTO getModelDetail(String modelId) {
        return findModelByModelId(modelId);
    }

    /**
     * 测试模型
     */
    @Override
    public Object testModel(String model) throws Exception {
        if (StringUtils.isBlank(model)) {
            throw new IllegalArgumentException("模型标识不能为空");
        }
        
        // 查找模型数据
        SystemModelDTO modelData = findModelByModelId(model);
        if (modelData == null) {
            throw new IllegalArgumentException("找不到模型");
        }
        
        // 设置请求头
        Map<String, String> headers = new HashMap<>();
        
        // 根据模型类型执行测试
        return modelTestService.testModel(modelData, headers);
    }

    /**
     * 更新模型
     */
    @Override
    public void updateModel(ModelUpdateRequest updateRequest) throws Exception {
        String modelId = updateRequest.getModel();
        if (StringUtils.isBlank(modelId)) {
            throw new IllegalArgumentException("模型标识不能为空");
        }
        
        // 查找模型数据
        SystemModelDTO modelData = findModelByModelId(modelId);

        // 查找数据库中的模型配置
        Optional<SystemModel> optDbModel = systemModelRepository.findByModel(modelId);
        
        // 合并元数据
        // 1. 系统配置（基础配置）
        Map<String, Object> baseMetadata = new HashMap<>();
        if (ObjectUtils.isNotEmpty(modelData)) {
            baseMetadata = objectMapper.convertValue(modelData, Map.class);
        }
        Map<String, Object> mergedMetadata = new HashMap<>(baseMetadata);
        
        // 2. 数据库配置（之前保存的配置）
        if (optDbModel.isPresent()) {
            Map<String, Object> dbMetadata = optDbModel.get().getMetadata();
            if (dbMetadata != null) {
                mergedMetadata.putAll(dbMetadata);
            }
        }
        
        // 3. 用户提交的最新配置
        if (updateRequest.getMetadata() != null) {
            mergedMetadata.putAll(updateRequest.getMetadata());
        }
        
        // 移除一些不需要保存的字段
        mergedMetadata.remove("avatar");
        mergedMetadata.remove("isCustom");
        
        // 强制赋值模型标识，避免元数据中的值覆盖真实标识
        mergedMetadata.put("model", modelId);
        
        // 如果名称存在，去除首尾空格
        if (mergedMetadata.containsKey("name") && mergedMetadata.get("name") instanceof String) {
            mergedMetadata.put("name", ((String) mergedMetadata.get("name")).trim());
        }
        
        // 删除 null 值
        mergedMetadata.entrySet().removeIf(entry -> entry.getValue() == null);
        
        // 保存到数据库
        SystemModel systemModel = optDbModel.orElse(new SystemModel());
        systemModel.setModel(modelId);
        systemModel.setMetadata(mergedMetadata);
        
        systemModelRepository.save(systemModel);
        
        // 重新加载所有模型
        ModelConfigManager.updateModelCache();
    }

    /**
     * 更新默认模型
     */
    @Override
    @Transactional
    public void updateDefaultModel(ModelUpdateDefaultRequest updateRequest) throws Exception {
        // 1. 移除所有默认标记
        Update unsetDefaultUpdate = new Update();
        unsetDefaultUpdate.unset("metadata.isDefault");
        unsetDefaultUpdate.unset("metadata.isDefaultDatasetTextModel");
        unsetDefaultUpdate.unset("metadata.isDefaultDatasetImageModel");
        
        mongoTemplate.updateMulti(
            new Query(),
            unsetDefaultUpdate,
            SystemModel.class
        );
        
        // 2. 设置新的默认模型
        if (StringUtils.isNotBlank(updateRequest.getLlm())) {
            updateModelDefaultFlag(updateRequest.getLlm(), "isDefault", true);
        }
        
        if (StringUtils.isNotBlank(updateRequest.getEmbedding())) {
            updateModelDefaultFlag(updateRequest.getEmbedding(), "isDefault", true);
        }
        
        if (StringUtils.isNotBlank(updateRequest.getTts())) {
            updateModelDefaultFlag(updateRequest.getTts(), "isDefault", true);
        }
        
        if (StringUtils.isNotBlank(updateRequest.getStt())) {
            updateModelDefaultFlag(updateRequest.getStt(), "isDefault", true);
        }
        
        if (StringUtils.isNotBlank(updateRequest.getRerank())) {
            updateModelDefaultFlag(updateRequest.getRerank(), "isDefault", true);
        }
        
        if (StringUtils.isNotBlank(updateRequest.getDatasetTextLLM())) {
            updateModelDefaultFlag(updateRequest.getDatasetTextLLM(), "isDefaultDatasetTextModel", true);
        }
        
        if (StringUtils.isNotBlank(updateRequest.getDatasetImageLLM())) {
            updateModelDefaultFlag(updateRequest.getDatasetImageLLM(), "isDefaultDatasetImageModel", true);
        }
        
        // 3. 重新加载所有模型
        ModelConfigManager.updateModelCache();
    }

    /**
     * 更新模型的默认标记
     */
    private void updateModelDefaultFlag(String modelId, String flag, boolean value) {
        Query query = new Query(Criteria.where("model").is(modelId));
        Update update = new Update().set("metadata." + flag, value);
        mongoTemplate.updateFirst(query, update, SystemModel.class);
    }

    /**
     * 删除模型
     */
    @Override
    public void deleteModel(String modelId) {
        // 查找模型
        SystemModelDTO modelItem = findModelByModelId(modelId);
        if (modelItem == null) {
            throw new IllegalArgumentException("找不到模型");
        }
        
        // 检查是否为系统模型（不允许删除）
        if (!Boolean.TRUE.equals(modelItem.getIsCustom())) {
            throw new IllegalArgumentException("系统模型不允许删除");
        }
        
        // 从数据库删除模型
        systemModelRepository.deleteByModel(modelId);
        
        // 重新加载所有模型
        ModelConfigManager.updateModelCache();
    }

    /**
     * 根据模型ID查找模型
     */
    @Override
    public SystemModelDTO findModelByModelId(String modelId) {
        // 从各类模型 Map 中查找
        Map<String, SystemModelDTO> llmMap = ModelConfigManager.getLlmModelMap();
        Map<String, SystemModelDTO> embeddingMap = ModelConfigManager.getEmbeddingModelMap();
        Map<String, SystemModelDTO> ttsMap = ModelConfigManager.getTtsModelMap();
        Map<String, SystemModelDTO> sttMap = ModelConfigManager.getSttModelMap();
        Map<String, SystemModelDTO> reRankMap = ModelConfigManager.getReRankModelMap();
        
        if (llmMap.containsKey(modelId)) {
            return llmMap.get(modelId);
        } else if (embeddingMap.containsKey(modelId)) {
            return embeddingMap.get(modelId);
        } else if (ttsMap.containsKey(modelId)) {
            return ttsMap.get(modelId);
        } else if (sttMap.containsKey(modelId)) {
            return sttMap.get(modelId);
        } else if (reRankMap.containsKey(modelId)) {
            return reRankMap.get(modelId);
        }
        
        // 如果在映射中找不到，遍历所有模型列表
        return ModelConfigManager.getSystemModelList().stream()
                .filter(m -> modelId.equals(m.getModel()))
                .findFirst()
                .orElse(null);
    }

    @Override
    public List<SystemModelDTO> findActiveModelList() {
        List<SystemModel> dbModels = systemModelRepository.findAll();
        List<SystemModelDTO> activeModels = new ArrayList<>();
        
        for (SystemModel dbModel : dbModels) {
            Map<String, Object> meta = dbModel.getMetadata();
            if (MapUtils.isEmpty(meta)) {
                continue;
            }
            
            try {
                SystemModelDTO dto = objectMapper.convertValue(meta, SystemModelDTO.class);
                dto.setIsCustom(true);
                
                // 检查模型是否活跃
                if (Boolean.TRUE.equals(dto.getIsActive())) {
                    activeModels.add(dto);
                }
            } catch (Exception e) {
                log.error("转换模型失败，模型名称: {}", dbModel.getModel(), e);
            }
        }
        
        return activeModels;
    }


    @SneakyThrows
    @Override
    public void updateWithJson(String configStr) {
        if (StringUtils.isBlank(configStr)) {
            throw new BussinessException("配置内容不能为空");
        }
        
        List<ModelUpdateRequest> modelUpdateRequestList;
        try {
            modelUpdateRequestList = JsonUtil.toJavaObjectList(configStr, ModelUpdateRequest.class);
        } catch (Exception e) {
            throw new BussinessException("配置格式解析失败: " + e.getMessage());
        }
        
        if (CollectionUtils.isEmpty(modelUpdateRequestList)) {
            return;
        }
        for (ModelUpdateRequest request : modelUpdateRequestList) {
            updateModel(request);
        }
    }

    /**
     * SystemModelDTO 转换为 Map<String, Object> metadata
     * @param systemModelDTO
     * @return
     */
    private Map<String, Object> convertDtoToMetadata(SystemModelDTO systemModelDTO) {
        Map<String, Object> metadata = new HashMap<>();
        // 使用反射获取SystemModelDTO的所有属性
        Field[] fields = SystemModelDTO.class.getDeclaredFields();
        List<String> excludeNameList = Arrays.asList("model");
        for (Field field : fields) {
            field.setAccessible(true);
            String name = field.getName();
            try {
                Object value = field.get(systemModelDTO);

                if (excludeNameList.contains(name)) {
                    continue;
                }
                if (value != null) {
                    metadata.put(field.getName(), value);
                }
            } catch (IllegalAccessException e) {
                log.debug("转换SystemModelDTO为metadata属性: {}出现错误",name, e);
            }
        }
        return metadata;
    }
}