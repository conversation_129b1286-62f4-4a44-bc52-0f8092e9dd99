/**
 * 文件信息DTO
 *
 * <AUTHOR>
 * date 2025-07-17
 * 描述：用于文件流读取、下载、预览等场景
 */

package com.sinitek.mind.support.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.File;

@Data
@AllArgsConstructor
public class FileInfo {
    @Schema(description = "文件ID")
    private String fileId;

    @Schema(description = "文件名")
    private String filename;

    @Schema(description = "文件本体")
    private transient File sourceFile;

    @Schema(description = "文件Content-Type类型")
    private String contentType;

    @Schema(description = "文件长度/字节数")
    private Long length;

    @Schema(description = "文件编码（如有）")
    private String encoding;
} 