package com.sinitek.mind.support.permission.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 资源权限对象
 * 从数据库中直接获取出来
 * 权限表：sprt_rightauth
 * 组织表：sprt_orgobject
 * <AUTHOR>
 * @date 2025/7/8
 */
@Data
@Schema(description = "资源权限对象")
public class Permission {

    @Schema(description = "主键，权限表中的objid")
    private String id;

    @Schema(description = "团队id，权限表中的tenant_id")
    private String teamId;

    @Schema(description = "部门id，从组织表中根据orgId判断对应的类型来设置")
    private String orgId;

    @Schema(description = "成员id，从组织表中根据orgId判断对应的类型来设置")
    private String tmbId;

    @Schema(description = "群组id，从组织表中根据orgId判断对应的类型来设置")
    private String groupId;

    @Schema(description = "资源类型，对应ResourceTypeEnum")
    private String resourceType;

    @Schema(description = "权限值-字符串类型，数据库中以字符串进行存储的,对应AuthTypeEnum")
    private String authType;

    @Schema(description = "权限值")
    private long permission;

    @Schema(description = "资源id")
    private String resourceId;
}
