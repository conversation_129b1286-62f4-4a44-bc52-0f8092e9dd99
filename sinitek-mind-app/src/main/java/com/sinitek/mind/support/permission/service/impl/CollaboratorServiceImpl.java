package com.sinitek.mind.support.permission.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.support.account.service.IAccountService;
import com.sinitek.mind.support.permission.dto.CollaboratorDTO;
import com.sinitek.mind.support.permission.dto.PermissionDTO;
import com.sinitek.mind.support.permission.enumerate.ResourceTypeEnum;
import com.sinitek.mind.support.permission.service.ICollaboratorService;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.spirit.org.core.IOrgFinder;
import com.sinitek.spirit.org.core.dto.EmployeeDTO;
import com.sinitek.spirit.org.core.dto.UnitDTO;
import com.sinitek.spirit.org.core.enumerate.UnitTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class CollaboratorServiceImpl implements ICollaboratorService {

    @Autowired
    private IPermissionService permissionService;

    @Autowired
    private IOrgFinder orgFinder;

    @Autowired
    private IAccountService accountService;

    @Override
    public List<String> getResourceClbsAndGroups(String teamId, String resourceId,
        String resourceType) {
        return List.of();
    }

    @Override
    public void syncCollaborators(String resourceId, String resourceType,
        List<String> collaborators, String teamId) {

    }

    @Override
    public void syncChildrenPermission(App app, String resourceType, List<String> collaborators) {

    }


    /**
     * 获取指定资源的协作者 只有app和dataset可用
     *
     * @param resourceId 资源id
     * @param resourceTypeEnum 资源类型
     */
    @Override
    public List<CollaboratorDTO> findClb(String resourceId,
        ResourceTypeEnum resourceTypeEnum) {
        Map<String, ? extends PermissionDTO> allAuthedOrg = permissionService.findAllAuthedOrg(
            resourceId, resourceTypeEnum);

        if (CollUtil.isEmpty(allAuthedOrg)) {
            return List.of();
        }

        String tenantId = CurrentUserFactory.getTenantId();

        return allAuthedOrg.entrySet().stream()
            .map(entry -> {
                CollaboratorDTO dto = new CollaboratorDTO();
                dto.setTeamId(tenantId);
                dto.setPermission(entry.getValue());

                judgeUnitTypeAndSetValue(entry.getKey(), dto);
                return dto;
            })
            .toList();
    }

    /**
     * 更新协作者 当资源类型为team时，资源id则为teamId
     *
     * @param resourceId 资源id
     * @param resourceTypeEnum 资源类型
     * @param permission 权限值-需要设置的权限值，可查看PermissionConstant中的定义
     * @param orgIdList 授权的对象，成员id、部门id、群组id
     */
    @Override
    public void updateClb(String resourceId, ResourceTypeEnum resourceTypeEnum, List<String> orgIdList, long permission) {
        permissionService.savePermission(resourceId, resourceTypeEnum, orgIdList, permission);
    }

    /**
     * 删除协作者 当资源类型为team时，资源id则为teamId
     *
     * @param resourceId 资源id
     * @param resourceTypeEnum 资源类型
     * @param orgId 需要删除的对象，成员id、部门id、群组id
     */
    @Override
    public void deleteClb(String resourceId, ResourceTypeEnum resourceTypeEnum,
        String orgId) {
        permissionService.deletePermission(resourceId, resourceTypeEnum, List.of(orgId));
    }

    /**
     * 判断对应org的类型，并设置协作者的值
     *
     * @param orgId 组织表的orgId
     * @param dto 协作者
     */
    private void judgeUnitTypeAndSetValue(String orgId, CollaboratorDTO dto) {
        UnitDTO unitDTO = orgFinder.getUnitById(orgId);
        if (unitDTO == null) {
            // 找不到组织结构，说明是员工
            EmployeeDTO employee = orgFinder.getEmployeeById(orgId);
            if (employee == null) {
                throw new BussinessException("找不到orgId对应的组织信息");
            }
            dto.setTmbId(orgId);
            dto.setName(employee.getName());
            dto.setAvatar(accountService.getAvatarByOrgId(orgId));
            return;
        }
        String unitType = unitDTO.getUnitType();
        dto.setName(unitDTO.getName());
        if (UnitTypeEnum.UNIT.toString().equals(unitType)) {
            // 部门
            dto.setOrgId(orgId);
        } else if (UnitTypeEnum.ROLE.toString().equals(unitType)) {
            // 群组
            dto.setGroupId(orgId);
        } else {
            // 成员
            dto.setTmbId(orgId);
        }
    }
}