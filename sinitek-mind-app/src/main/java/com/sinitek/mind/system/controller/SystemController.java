package com.sinitek.mind.system.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.support.ModelConfigManager;
import com.sinitek.mind.system.dto.DefaultModelsDTO;
import com.sinitek.mind.system.dto.InitDataResponseDTO;
import com.sinitek.mind.system.service.AuthService;
import com.sinitek.mind.system.support.SystemGlobals;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 系统 Controller 层
 *
 * <AUTHOR>
 * date 2025-07-01
 */
@RestController
@RequestMapping("/mind/api/common/system")
@Tag(name = "AI系统通用")
public class SystemController {

    @Autowired
    private AuthService authService;

    @GetMapping("/getInitData")
    @Operation(summary = "获取系统初始化数据")
    public ApiResponse<InitDataResponseDTO> getInitData(
            @RequestParam(value = "bufferId", required = false) String bufferId,
            HttpServletRequest request) {
        List<SystemModelDTO> activeModelList = ModelConfigManager.getSystemActiveModelList();
        DefaultModelsDTO defaultModels = ModelConfigManager.getLatestDefaultModels(activeModelList);
        String currentBufferId = SystemGlobals.getSystemInitBufferId();
        String systemVersion = SystemGlobals.getSystemVersion();
        String referer = request.getHeader("referer");

        try {
            authService.authCert();
            if (StringUtils.isNotBlank(bufferId) && StringUtils.isNotBlank(currentBufferId) && bufferId.equals(currentBufferId)) {
                return ApiResponse.success(InitDataResponseDTO.onlyBufferId(currentBufferId, systemVersion));
            }
            return ApiResponse.success(
                    InitDataResponseDTO.full(
                            currentBufferId,
                            SystemGlobals.getFeConfigs(),
                            SystemGlobals.getSubPlans(),
                            systemVersion,
                            activeModelList,
                            defaultModels
                    )
            );
        } catch (Exception e) {
            if (StringUtils.isNotBlank(referer) && referer.contains("/price")) {
                return ApiResponse.success(
                        InitDataResponseDTO.pricePage(
                                SystemGlobals.getFeConfigs(),
                                SystemGlobals.getSubPlans(),
                                activeModelList
                        )
                );
            }
            String unAuthBufferId = StringUtils.isNotBlank(currentBufferId) ? "unAuth_" + currentBufferId : "";
            if (StringUtils.isNotBlank(bufferId) && unAuthBufferId.equals(bufferId)) {
                return ApiResponse.success(InitDataResponseDTO.onlyBufferId(unAuthBufferId, null));
            }
            return ApiResponse.success(InitDataResponseDTO.unAuth(unAuthBufferId, SystemGlobals.getFeConfigs()));
        }
    }

} 