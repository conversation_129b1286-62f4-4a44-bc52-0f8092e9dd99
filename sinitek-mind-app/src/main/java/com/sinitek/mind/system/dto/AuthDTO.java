package com.sinitek.mind.system.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 认证结果DTO
 *
 * <AUTHOR>
 * date 2025-07-02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthDTO {
    
    @Schema(description = "用户ID")
    private String userId;
    
    @Schema(description = "团队ID")
    private String teamId;
    
    @Schema(description = "团队成员ID")
    private String tmbId;
    
    @Schema(description = "API密钥")
    private String apikey;
    
    @Schema(description = "认证类型")
    private String authType;
    
    @Schema(description = "是否是所有者")
    private Boolean isOwner;
} 