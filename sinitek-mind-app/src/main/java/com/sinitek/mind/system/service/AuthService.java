package com.sinitek.mind.system.service;

import com.sinitek.mind.system.dto.AuthDTO;
import com.sinitek.sirm.common.utils.SpringMvcUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Service;

/**
 * 系统认证 Service 层
 *
 * <AUTHOR>
 * date 2025-07-01
 */
@Service
public class AuthService {

    /**
     * 认证接口
     *
     * @return 认证信息DTO
     * @throws Exception 认证失败时抛出异常
     */
    public AuthDTO authCert() {
        // 获取当前请求
        HttpServletRequest request = SpringMvcUtil.getHttpServletRequest();
        if (request == null) {
            throw new IllegalStateException("无法获取当前请求");
        }
        // TODO: 后续明确Java版本用户管理方式在进行实现，考虑走拦截器统一实现
        
        // 返回认证信息
        return AuthDTO.builder()
                .userId("6858aadda9fd281fe94237b7")
                .teamId("685e0b4574813aa1df06dbd7")
                .tmbId("685e0b4574813aa1df06dbde")
                .isOwner(true)
                .build();
    }
} 