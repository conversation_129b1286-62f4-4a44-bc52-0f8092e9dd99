package com.sinitek.mind.system.support;

import com.sinitek.mind.config.SinicubeMindProperties;
import com.sinitek.mind.model.support.ModelConfigManager;
import com.sinitek.mind.system.dto.*;
import com.sinitek.mind.system.service.SystemConfigService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 系统全局变量工具类
 *
 * <AUTHOR>
 * date 2025-07-01
 */
@Component
public class SystemGlobals implements ApplicationRunner {

    private static String systemInitBufferId;
    private static String systemVersion;
    private static FeConfigsDTO feConfigs;
    private static SystemEnvDTO systemEnv;
    private static Object subPlans;
    private static LicenseDataDTO licenseData;
    private static SecurityEnvDTO securityEnv;
    private static SinicubeMindProperties mindProperties;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private ConfigFileReader configFileReader;

    @Autowired
    private ModelConfigManager modelConfigManager;

    @Autowired
    private SinicubeMindProperties sinicubeMindProperties;

    /**
     * 应用启动后执行初始化
     *
     * @param args 应用参数
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        modelConfigManager.loadAllModels();
        initSystemConfig();
    }

    /**
     * 初始化系统配置
     */
    private void initSystemConfig() {
        try {
            // 从数据库获取配置
            Map<String, Object> dbConfig = systemConfigService.getFastGPTConfigFromDB();
            FastGPTConfigDTO fastgptConfig = (FastGPTConfigDTO) dbConfig.get("fastgptConfig");
            licenseData = (LicenseDataDTO) dbConfig.get("licenseData");
            systemInitBufferId = (String) dbConfig.get("systemInitBufferId");

            // 从配置文件读取配置
//            FastGPTConfigDTO fileConfig = configFileReader.readConfigDataAsObject("config.json", FastGPTConfigDTO.class);

            // 创建默认前端配置
            FeConfigsDTO defaultFeConfigs = createDefaultFeConfigs();

            // 合并配置（文件配置/环境变量配置 < 默认配置 < 数据库配置 < 运行时动态数据）
            // 环境变量配置
            FeConfigsDTO mergedFeConfigs = sinicubeMindProperties.getFeConfigs();
            
            // 1. 文件配置
//            if (fileConfig.getFeConfigs() != null) {
//                mergedFeConfigs = fileConfig.getFeConfigs();
//            }
            
            // 2. 默认配置
            mergeFeConfigs(mergedFeConfigs, defaultFeConfigs);
            
            // 3. 数据库配置
            if (fastgptConfig.getFeConfigs() != null) {
                mergeFeConfigs(mergedFeConfigs, fastgptConfig.getFeConfigs());
            }
            
            // 4. 运行时动态数据
            // mergedFeConfigs.setIsPlus(licenseData != null);
            mergedFeConfigs.setShow_aiproxy(StringUtils.isNotBlank(System.getenv("AIPROXY_API_ENDPOINT")));
            mergedFeConfigs.setShow_coupon("true".equals(System.getenv("SHOW_COUPON")));
            
            // 合并系统环境配置
            // 环境变量配置
            SystemEnvDTO mergedSystemEnv = sinicubeMindProperties.getSystemEnv();
            
            // 1. 文件配置
//            if (fileConfig.getSystemEnv() != null) {
//                mergedSystemEnv = fileConfig.getSystemEnv();
//            }
            
            // 2. 数据库配置
            if (fastgptConfig.getSystemEnv() != null) {
                // 复制数据库配置到合并后的配置
                if (fastgptConfig.getSystemEnv().getOpenapiPrefix() != null) {
                    mergedSystemEnv.setOpenapiPrefix(fastgptConfig.getSystemEnv().getOpenapiPrefix());
                }
                // ... 其他字段的合并逻辑
            }

            // 设置计算属性
            boolean showCustomPdfParse = false;
            if (mergedSystemEnv.getCustomPdfParse() != null) {
                showCustomPdfParse = StringUtils.isNotBlank(mergedSystemEnv.getCustomPdfParse().getUrl()) || 
                                     StringUtils.isNotBlank(mergedSystemEnv.getCustomPdfParse().getDoc2xKey());
                mergedFeConfigs.setShowCustomPdfParse(showCustomPdfParse);
                mergedFeConfigs.setCustomPdfParsePrice(mergedSystemEnv.getCustomPdfParse().getPrice());
            }

            // 保存最终配置
            feConfigs = mergedFeConfigs;
            systemEnv = mergedSystemEnv;
            subPlans = fastgptConfig.getSubPlans();

            securityEnv = new SecurityEnvDTO();
            if (sinicubeMindProperties.getSecurityEnv() != null) {
                securityEnv = sinicubeMindProperties.getSecurityEnv();
            }

            // 初始化系统版本
            initSystemVersion();
        } catch (Exception e) {
            // 异常处理
            e.printStackTrace();
        }
    }

    /**
     * 创建默认前端配置
     */
    private FeConfigsDTO createDefaultFeConfigs() {
        FeConfigsDTO defaultConfig = new FeConfigsDTO();
        defaultConfig.setShow_emptyChat(true);
        defaultConfig.setShow_git(true);
        defaultConfig.setDocUrl("https://doc.tryfastgpt.ai");
        defaultConfig.setOpenAPIDocUrl("https://doc.tryfastgpt.ai/docs/development/openapi");
        defaultConfig.setSystemPluginCourseUrl("https://fael3z0zfze.feishu.cn/wiki/ERZnw9R26iRRG0kXZRec6WL9nwh");
        defaultConfig.setAppTemplateCourse("https://fael3z0zfze.feishu.cn/wiki/CX9wwMGyEi5TL6koiLYcg7U0nWb?fromScene=spaceOverview");
        defaultConfig.setSystemTitle("FastGPT");
        defaultConfig.setConcatMd("项目开源地址: [FastGPT GitHub](https://github.com/labring/FastGPT)\n交流群: ![](https://oss.laf.run/otnvvf-imgs/fastgpt-feishu1.png)");
        
        defaultConfig.setFavicon("/favicon.ico");
        defaultConfig.setUploadFileMaxSize(500);
        
        return defaultConfig;
    }

    /**
     * 合并前端配置
     */
    private void mergeFeConfigs(FeConfigsDTO target, FeConfigsDTO source) {
        if (source == null) {
            return;
        }
        
        // 这里只列举部分字段，实际应该包含所有字段
        if (source.getLafEnv() != null) {
            target.setLafEnv(source.getLafEnv());
        }
        if (source.getMcpServerProxyEndpoint() != null) {
            target.setMcpServerProxyEndpoint(source.getMcpServerProxyEndpoint());
        }
        if (source.getShow_emptyChat() != null) {
            target.setShow_emptyChat(source.getShow_emptyChat());
        }
        if (source.getShow_git() != null) {
            target.setShow_git(source.getShow_git());
        }
        if (source.getDocUrl() != null) {
            target.setDocUrl(source.getDocUrl());
        }
        if (source.getOpenAPIDocUrl() != null) {
            target.setOpenAPIDocUrl(source.getOpenAPIDocUrl());
        }
        if (source.getSystemPluginCourseUrl() != null) {
            target.setSystemPluginCourseUrl(source.getSystemPluginCourseUrl());
        }
        if (source.getAppTemplateCourse() != null) {
            target.setAppTemplateCourse(source.getAppTemplateCourse());
        }
        if (source.getSystemTitle() != null) {
            target.setSystemTitle(source.getSystemTitle());
        }
        if (source.getConcatMd() != null) {
            target.setConcatMd(source.getConcatMd());
        }
        if (source.getLimit() != null) {
            target.setLimit(source.getLimit());
        }
        if (source.getScripts() != null) {
            target.setScripts(source.getScripts());
        }
        if (source.getFavicon() != null) {
            target.setFavicon(source.getFavicon());
        }
        if (source.getUploadFileMaxSize() != null) {
            target.setUploadFileMaxSize(source.getUploadFileMaxSize());
        }
    }

    /**
     * 初始化系统版本
     */
    private void initSystemVersion() {
        try {
            // 从环境变量或配置文件获取系统版本
            systemVersion = System.getProperty("app.version", "1.0.0");
        } catch (Exception e) {
            systemVersion = "1.0.0";
        }
    }

    /**
     * 获取系统初始化缓冲ID
     */
    public static String getSystemInitBufferId() {
        return systemInitBufferId;
    }

    /**
     * 获取系统版本
     */
    public static String getSystemVersion() {
        return systemVersion;
    }

    /**
     * 获取前端配置
     */
    public static FeConfigsDTO getFeConfigs() {
        return feConfigs;
    }

    /**
     * 获取订阅计划
     */
    public static Object getSubPlans() {
        return subPlans;
    }

    /**
     * 获取系统环境配置
     */
    public static SystemEnvDTO getSystemEnv() {
        return systemEnv;
    }

    /**
     * 获取许可证数据
     */
    public static LicenseDataDTO getLicenseData() {
        return licenseData;
    }

    /**
     * 获取安全配置
     * @return
     */
    public static SecurityEnvDTO getSecurityEnv() {
        return securityEnv;
    }

    public static String getSandboxUrl() {
        return mindProperties.getSandboxUrl();
    }
}